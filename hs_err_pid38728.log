#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 8323072 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3618), pid=38728, tid=15312
#
# JRE version:  (17.0.15+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.15+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dcool.request.port=2349 -Dsocket.server.port=33833 -javaagent:D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\lib\idea_rt.jar=6814 -Dfile.encoding=UTF-8 com.gtyyj.SyncReport

Host: 12th Gen Intel(R) Core(TM) i5-12500H, 16 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Thu Aug 21 10:52:49 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 2.019811 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x00000269648f5c20):  JavaThread "Unknown thread" [_thread_in_vm, id=15312, stack(0x0000007a96900000,0x0000007a96a00000)]

Stack: [0x0000007a96900000,0x0000007a96a00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x685b79]
V  [jvm.dll+0x83d7fa]
V  [jvm.dll+0x83f2be]
V  [jvm.dll+0x83f923]
V  [jvm.dll+0x24c54f]
V  [jvm.dll+0x682899]
V  [jvm.dll+0x67736a]
V  [jvm.dll+0x30c4eb]
V  [jvm.dll+0x3139e6]
V  [jvm.dll+0x364583]
V  [jvm.dll+0x3647af]
V  [jvm.dll+0x2e34fc]
V  [jvm.dll+0x2e4454]
V  [jvm.dll+0x80f01b]
V  [jvm.dll+0x3722e1]
V  [jvm.dll+0x7eda65]
V  [jvm.dll+0x3f5fee]
V  [jvm.dll+0x3f7b71]
C  [jli.dll+0x5347]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffdfdc2ffd8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000026964963080 GCTaskThread "GC Thread#0" [stack: 0x0000007a96a00000,0x0000007a96b00000] [id=32528]
  0x0000026964974900 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000007a96b00000,0x0000007a96c00000] [id=22104]
  0x00000269649761d0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000007a96c00000,0x0000007a96d00000] [id=40896]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffdfd3e3bd7]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000269648f15a0] Heap_lock - owner thread: 0x00000269648f5c20

Heap address: 0x0000000605800000, size: 8104 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000605800000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffdfd7cecc9]

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.012 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.15\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6c2f30000 - 0x00007ff6c2f3e000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\java.exe
0x00007fff21860000 - 0x00007fff21ac5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff1f990000 - 0x00007fff1fa59000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff1f170000 - 0x00007fff1f558000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff1eb30000 - 0x00007fff1ec7b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff0f0b0000 - 0x00007fff0f0c9000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\jli.dll
0x00007fff04920000 - 0x00007fff0493e000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\VCRUNTIME140.dll
0x00007fff1f640000 - 0x00007fff1f80a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff1ef10000 - 0x00007fff1ef37000 	C:\WINDOWS\System32\win32u.dll
0x00007ffef4530000 - 0x00007ffef47ca000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff1f610000 - 0x00007fff1f63b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff21770000 - 0x00007fff21819000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff1ed10000 - 0x00007fff1ee47000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff1ef40000 - 0x00007fff1efe3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff20b60000 - 0x00007fff20b90000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff0ef40000 - 0x00007fff0ef4c000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\vcruntime140_1.dll
0x00007fff04590000 - 0x00007fff0461d000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\msvcp140.dll
0x00007ffdfd0f0000 - 0x00007ffdfdd65000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\server\jvm.dll
0x00007fff215a0000 - 0x00007fff21653000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff1fdf0000 - 0x00007fff1fe96000 	C:\WINDOWS\System32\sechost.dll
0x00007fff20a40000 - 0x00007fff20b55000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff21660000 - 0x00007fff216d4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff1d5d0000 - 0x00007fff1d62e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff1a7f0000 - 0x00007fff1a825000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff158d0000 - 0x00007fff158db000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff1d5b0000 - 0x00007fff1d5c4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff1d890000 - 0x00007fff1d8ab000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff0eef0000 - 0x00007fff0eefa000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\jimage.dll
0x00007fff1bd70000 - 0x00007fff1bfb1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff1fa60000 - 0x00007fff1fde5000 	C:\WINDOWS\System32\combase.dll
0x00007fff20000000 - 0x00007fff200e1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffefad70000 - 0x00007ffefada9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff1f560000 - 0x00007fff1f5f9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff0ce00000 - 0x00007fff0ce0e000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\instrument.dll
0x00007fff04560000 - 0x00007fff04585000 	C:\Users\<USER>\.jdks\azul-17.0.15\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\azul-17.0.15\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Users\<USER>\.jdks\azul-17.0.15\bin\server

VM Arguments:
jvm_args: -Dcool.request.port=2349 -Dsocket.server.port=33833 -javaagent:D:\Program Files\JetBrains\IntelliJ IDEA 2025.1\lib\idea_rt.jar=6814 -Dfile.encoding=UTF-8 
java_command: com.gtyyj.SyncReport
java_class_path (initial): C:\work\code\gtyyj\target\classes;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel\3.3.4\easyexcel-3.3.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel-core\3.3.4\easyexcel-core-3.3.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel-support\3.3.4\easyexcel-support-3.3.4.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.13\commons-codec-1.13.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.19\commons-compress-1.19.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.06\curvesapi-1.06.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\3.1.0\xmlbeans-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;C:\Users\<USER>\.m2\repository\org\ehcache\ehcache\3.9.9\ehcache-3.9.9.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.32\slf4j-api-1.7.32.jar;C:\Users\<USER>\.m2\repository\io\gitee\mcolley\swak-spring-boot-starter\2.0.0\swak-spring-boot-starter-2.0.0.jar;C:\Users\<USER>\.m2\repository\io\gitee\mcolley\swak-core\2.0.0\swak-core-2.0.0.jar;C:\Users\<USER>\.m2\repository\io\gitee\mcolley\swak-common\2.0.0\swak-common-2.0.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8497659904                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8497659904                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\zulu11.43.55-ca-jdk11.0.9.1-win_x64
PATH=D:\Program Files\Seelen\Seelen UI;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;%JAVA_HOME%\bin;C:\work\tools\apache-maven-3.6.3\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\dotnet\;%NVM_HOME%;%NVM_SYMLINK%;D:\Program Files\NetSarang\Xshell 8\;D:\Program Files\NetSarang\Xftp 8\;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\;%SYSTEMROOT%\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Programs\nvm;D:\Programs\nodejs;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;;%USERPROFILE%\AppData\Local\Microsoft\WindowsApps
USERNAME=kratzer
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 6 days 1:05 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x434, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 1
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 2
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 3
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 4
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 5
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 6
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 7
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 8
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 9
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 10
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 11
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 12
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 13
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 14
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 15
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500

Memory: 4k page, system-wide physical 32400M (2844M free)
TotalPageFile size 42640M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 14M, peak: 14M
current process commit charge ("private bytes"): 580M, peak: 588M

vm_info: OpenJDK 64-Bit Server VM (17.0.15+6-LTS) for windows-amd64 JRE (17.0.15+6-LTS) (Zulu17.58+21-CA), built on Apr  4 2025 15:28:26 by "zulu_re" with MS VC++ 16.10 / 16.11 (VS2019)

END.
