import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * DGRoutineResultSync
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/28 14:40
 */
public class DGRoutineResultSync {
    static String paramFmt = "{\"startDate\":\"%sT00:00:00\",\"endDate\":\"%sT00:00:00\"}";

    public static void main(String[] args) throws Exception {
        // System.out.println(URLDecoder.decode("id=19&executorParam=%7B%22startDate%22%3A%222025-03-25T00%3A00%3A00%22%2C%22endDate%22%3A%222025-04-01T00%3A00%3A00%22%7D&addressList=", StandardCharsets.UTF_8.toString()));
        // System.out.println(URLEncoder.encode("id=19&executorParam={\"startDate\":\"2025-03-25T00:00:00\",\"endDate\":\"2025-04-01T00:00:00\"}&addressList=", StandardCharsets.UTF_8.toString()));

        final String start = args[0];
        final List<String> params = buildParams(start);

        System.out.println("params = \n" + String.join("\n", params));

        // 处理每个分块
        for (String param : params) {
            // 执行xxljob请求
            triggerXxlJob(param);
        }

    }

    /**
     * @param start 开始时间
     * @return 参数集合
     */
    private static List<String> buildParams(String start) {
        List<String> params = new ArrayList<>();

        LocalDate startDate = LocalDate.parse(start);

        for (int i = 0; i < 6; i++) {
            int add = i == 0 ? 4 : 5;
            LocalDate endDate = startDate.plusDays(add);
            if (i == 5) {
                endDate = startDate.plusMonths(1).withDayOfMonth(1);
            }
            params.add(0, String.format(paramFmt, startDate, endDate));

            startDate = endDate;
        }

        return params;
    }

    /**
     * 触发XXL-JOB任务
     * @param executorParam 执行参数（条码列表）
     */
    private static void triggerXxlJob(String executorParam) {
        System.out.println("开始执行任务 参数：[" + executorParam + "]");

        try {
            // 创建URL连接
            URL url = new URL("http://************:5656/xxl-job-admin/jobinfo/trigger");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法
            connection.setRequestMethod("POST");

            // 设置请求头
            connection.setRequestProperty("Accept", "application/json, text/javascript, */*; q=0.01");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            connection.setRequestProperty("X-Requested-With", "XMLHttpRequest");
            connection.setRequestProperty("Cookie", "XXL_JOB_LOGIN_IDENTITY=7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d");

            // 超时时间3秒
            connection.setConnectTimeout(3000);

            // 允许输入输出
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 构建请求体
            String requestBody = "id=19&executorParam=" + URLEncoder.encode(executorParam, StandardCharsets.UTF_8.toString()) + "&addressList=";

            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ? connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {

                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }

                System.out.println("Response: " + response.toString());
            }

        } catch (Exception e) {
            System.err.println("调用XXL-JOB接口失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
