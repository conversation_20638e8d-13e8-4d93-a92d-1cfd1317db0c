package com.gtyyj;

import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * <pre>
 * SelectDataFromSQLServer
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/7 17:21
 */
public class SelectDataFromSQLServer {

    public static void main(String[] args) {

        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");

            String connectionUrl = "***********************************************************************************************************************";

            Connection connection = DriverManager.getConnection(
                    connectionUrl, "Lab_lwsq", "lab@123");

            PreparedStatement preparedStatement = connection.prepareStatement("select F_BLZD from PIS_RESULT_MAIN where RESULT_ID = 'lw23_I24000234_202406263002550549';");

            ResultSet resultSet = preparedStatement.executeQuery();

            while (resultSet.next()) {
                System.out.println(new String(resultSet.getBytes("F_BLZD"), Charset.forName("GBK")));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
