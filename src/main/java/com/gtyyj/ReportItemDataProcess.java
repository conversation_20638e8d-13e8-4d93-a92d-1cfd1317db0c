package com.gtyyj;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import lombok.Data;
import lombok.ToString;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <pre>
 * ReportItemDataProcess
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/7/28 16:23
 */
public class ReportItemDataProcess {

    private static final String sql_fmt = "INSERT INTO `labway-lis-region`.tb_customer_report_item_template (template_id, org_id, org_name, customer_id, customer_name, report_item_code, report_item_name, remark, is_delete, operator, operator_id, create_time, update_time)\n" +
            "VALUES ('{}', '1', 'org2', '1878709252063698945', '丹阳妇幼保健院', '{}', '{}', '', 0, 'admin', 'admin', now(), now());\n";

    public static void main(String[] args) throws Exception {
        final File sql = new File("C:\\work\\文档\\丹阳\\丹阳妇幼\\reportitem.sql");
        if (!sql.exists()) {
            sql.createNewFile();
        }

        final File resultDir = new File("C:\\work\\文档\\丹阳\\丹阳妇幼");

        AtomicInteger cnt = new AtomicInteger(0);
        AtomicInteger totalCnt = new AtomicInteger(0);

        Set<String> unique = new HashSet<>();
        final List<Row> list = new ArrayList<Row>();
        AnalysisEventListener<Row> readListener = new AnalysisEventListener<>() {
            /**
             * 这个每一条数据解析都会来调用
             */
            @Override
            public void invoke(Row data, AnalysisContext context) {
                // System.out.println("解析到一条数据:" + JSON.toJSONString(data));
                if (unique.add(data.toString())) {
                    list.add(data);
                    totalCnt.incrementAndGet();

                    try {
                        Files.writeString(sql.toPath(), StrUtil.format(sql_fmt, data.no, data.reportCode, data.reportName), StandardOpenOption.APPEND);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    System.out.println("这条数据重复了 " + data.toString());
                }
            }

            /**
             * 所有数据解析完成了 都会来调用
             */
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {

            }
        };

        for (File file : Objects.requireNonNull(resultDir.listFiles())) {
            if (!file.getName().endsWith(".xlsx")) {
                continue;
            }

            // file = new File("C:\\work\\文档\\社区云LIS\\上海内网\\北新泾\\Result0.xlsx");
            ExcelReader excelReader = EasyExcel.read(file).build();
            // 这里为了简单 所以注册了 同样的head 和Listener 自己使用功能必须不同的Listener
            // readSheet参数设置读取sheet的序号
            ReadSheet readSheet1 =
                    EasyExcel.readSheet(0).head(Row.class).registerReadListener(readListener).build();
            // 这里注意 一定要把sheet1 sheet2 一起传进去，不然有个问题就是03版的excel 会读取多次，浪费性能
            excelReader.read(readSheet1);
            // 这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
            excelReader.finish();

            System.out.println(StrUtil.format("文件 {} 数据总行数 {}", file.getName(), totalCnt.get()));

            cnt.addAndGet(totalCnt.get());
            totalCnt.set(0);

        }

        System.out.println("总条数 " + cnt.get());

    }

    @Data
    @ToString
    public static class Row {
        @ExcelProperty(index = 0)
        private Integer no;
        @ExcelProperty(index = 1)
        private String reportCode;
        @ExcelProperty(index = 2)
        private String reportName;
    }


}
