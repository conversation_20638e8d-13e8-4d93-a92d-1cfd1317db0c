package com.gtyyj;

import com.swak.frame.util.StringPool;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.HashSet;
import java.util.Set;

/**
 * <pre>
 * QingxiLog
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/1 16:43
 */
public class QingxiLog {

    public static void main(String[] args) throws Exception {
        final String logFile = "C:\\work\\logs\\dongguan-local-qingxi-jianyan.log.2025-08-01.2480\\dongguan-local-qingxi-jianyan.log.2025-08-01.2480";

        File barcodeFile = Path.of("C:\\work\\logs\\dongguan-local-qingxi-jianyan.log.2025-08-01.2480\\out_barcodes.txt").toFile();

        if (barcodeFile.exists()) {
            barcodeFile.delete();
        }
        barcodeFile.createNewFile();

        final Set<String> outBarcodes = new HashSet<>();

        try (BufferedReader br = new BufferedReader(new FileReader(logFile, Charset.forName("GBK")))) {
            String line;
            while ((line = br.readLine()) != null) { // 逐行读取
                if (line.contains("原始地址") || line.contains("东莞清溪结果回传成功") || line.contains("保存检验结果【医院条码号")) {
                    System.out.println("line = " + line);
                    if (line.contains("保存检验结果【医院条码号")) {
                        outBarcodes.add(line.substring(line.indexOf("医院条码号 ") + "医院条码号 ".length(), line.indexOf(" 实验室条码号 ")));
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        for (String line : outBarcodes) {
            Files.writeString(barcodeFile.toPath(), line + StringPool.NEW_LINE, StandardOpenOption.APPEND);
        }
    }

}
