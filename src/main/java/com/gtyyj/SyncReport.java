package com.gtyyj;

import cn.hutool.core.util.StrUtil;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.net.http.HttpResponse;

/**
 * <pre>
 * SyncReport
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/21 10:50
 */
public class SyncReport {


    public static void main(String[] args) {

        // String barcodes = "900000473027,900000473005,900000473037,900000473114,900000473245,900000473287,900000473237,900000473205,900000473365,900000473371,900000473341,900000473497,900000473477,900000473439,5029770,5159153,900000481969,900000481929,900000481917,900000481909,900000481967,900000481935,900000482017,900000481901,900000481971,900000481977,900000481961,900000482037,900000482051,900000481975,900000481957,900000482015,900000482055,900000482025,900000482033,900000482013,900000481927,900000481995,900000481991,900000481981,900000482023,900000481993,900000482001,900000482067,900000482009,900000481973,900000482093,900000482085,900000482049,900000482019,900000482083,900000481997,900000481987,900000482045,900000482065,900000482057,900000482003,900000482095,900000482081,900000482061,900000482041,900000482007,900000482075,900000482087,900000482077,900000482089,900000482115,900000482119,900000482113,900000482103,900000482123,900000482099,900000482125,900000482109,900000482107,900000482131,900000482129,900000482105,5166450,900000482232,900000482172,900000482254,900000482202,900000482147,900000482190,900000482200,900000482137,900000482176,900000482184,900000482208,900000482265,900000482212,900000482297,900000482242,900000482188,900000482256,900000482174,900000482141,900000482180,900000482145,900000482149,900000482151,900000482143,900000482250,900000482178,900000482238,900000482194,900000482214,900000482218,900000482139,900000482220,900000482153,900000482222,900000482170,900000482182,900000482210,900000482198,900000482279,900000482301,900000482273,900000482271,900000482246,900000482234,900000482260,900000482216,900000482258,900000482277,900000482289,900000482305,900000482339,900000482317,900000482291,900000482327,900000482309,900000482319,900000482307,900000482353,900000482281,900000482321,900000482293,900000482355,900000482325,900000482357,900000482351,900000482349,900000482331,900000482359,900000482363,900000482361,900000482365,900000482373,900000482375,900000482379,900000482377,900000482371,900000482369,900000482381,900000482367";
        String barcodes = "900000481987";


        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "hgsDGlQIdV6iXp58BXG1EK1uCv+vMMkkwxtmpUyBqyUOTeu+4VTjmfogSakqflG5fZd830ETuYbj/hbhkxCnKg==");
        headers.set("Labway_sso_clientid", "80808089888baab201888bb7dd2d0002");
        headers.set("Labway_sso_token", "Bx2YmnOkYRpzRO2GYw9UFHt3Utk1usn+OfNrf2UTnJWMv4/0/j0IHG8v7B9ceMQMJ+54/HzIWhq8AWdPqZCfWg==");
        headers.setContentType(MediaType.APPLICATION_JSON);

        for (String s : barcodes.split(",")) {
            // 设置请求体
            String requestBody = "{" +
                    "  \"barcode\": \"{}\"," +
                    "  \"page\": 1," +
                    "  \"size\": 10000," +
                    "  \"outBarcodes\": []" +
                    "}";

            // 创建 HttpEntity
            HttpEntity<String> entity = new HttpEntity<>(StrUtil.format(requestBody, s), headers);

            // 发送 POST 请求
            String url = "https://center.labway.cn/compare-result/result/syncReportResult";
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            System.out.println(response.getBody());
        }

    }

}
