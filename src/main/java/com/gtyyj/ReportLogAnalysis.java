package com.gtyyj;

import com.swak.frame.util.StringPool;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <pre>
 * ReportLogAnalysis
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/2/14 18:11
 */
public class ReportLogAnalysis {

    public static void main(String[] args) throws Exception {

        String fmt = "{\"orgId\":\"%s\",\"hspOrgCode\":\"%s\",\"itemType\":\"%s\",\"barcode\":\"%s\"}";

        String start = "SampleRecordSyncClientImpl     : LIMS ";
        String end = " 报告同步失败 ";

        Set<String> jsons = new HashSet<>();

        Files.list(Path.of("C:\\logs")).forEach(e -> {
            try {
                Files.readAllLines(e).forEach(line -> {

                    if (line.contains(start) && line.contains(end)) {
                        String customerBarcode = line.substring(line.indexOf(start) + start.length(), line.indexOf(end));
                        String[] split = customerBarcode.split(StringPool.UNDERLINE);
                        if (split.length == 3) {
                            jsons.add(String.format(fmt, split[0], split[1], "", split[2]));
                        }
                        if (split.length == 4) {
                            jsons.add(String.format(fmt, split[0], split[1], split[2], split[3]));
                        }

                    }

                });
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }

        });

        System.out.println(String.join("\n", jsons));

        // AtomicInteger index = new AtomicInteger(1);
        // for (List<String> part : Lists.partition(new ArrayList<>(jsons), 100)) {
        //     Files.write(
        //             Path.of(String.format("C:\\logs\\param_%s.json", index.getAndIncrement())),
        //             part.stream().collect(Collectors.joining(",\n", "[\n", "\n]")).getBytes(StandardCharsets.UTF_8), StandardOpenOption.CREATE_NEW);
        // }

        Files.write(
                Path.of("C:\\logs\\param.txt"),
                jsons.stream().collect(Collectors.joining(",\n", "[\n", "\n]")).getBytes(StandardCharsets.UTF_8), StandardOpenOption.CREATE_NEW);

    }

}
