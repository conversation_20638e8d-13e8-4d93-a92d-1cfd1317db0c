package com.gtyyj;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <pre>
 * ESDataExport
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/7 17:52
 */
public class ESDataExport {


    public static void main(String[] args) {

        try {
            // 1. 创建 RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();

            // 2. 设置 Basic Auth 认证头
            // String auth = "admin:Huawei@1234!"; // 替换为实际用户名密码
            // String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            // headers.set("Authorization", "Basic " + encodedAuth); // 关键：格式为 "Basic [Base64编码]"

            // 2. 设置 Basic Auth 认证头
            restTemplate.getInterceptors().add(new BasicAuthenticationInterceptor("admin", "Huawei@1234!"));

            // 3. 添加其他自定义 Header（可选）
            headers.set("Content-Type", "application/json");

            String must = """
                    [
                        {
                            "range": {
                                "finalCheckDate": {
                                    "from": 1751299200000,
                                    "to": 1753977600000,
                                    "include_lower": true,
                                    "include_upper": true,
                                    "boost": 1.0
                                }
                            }
                        },
                        {
                            "term": {
                                "sampleStatus": {
                                    "value": 30,
                                    "boost": 1.0
                                }
                            }
                        },
                        {
                            "term": {
                                "isDelete": {
                                    "value": 0,
                                    "boost": 1.0
                                }
                            }
                        },
                        {
                            "exists": {
                                "field": "sampleProperty"
                            }
                        }
                    ]
                    """;
            String mustNot = """
                    [
                        {
                            "term": {
                                "sampleProperty": {
                                    "value": "正常",
                                    "boost": 1.0
                                }
                            }
                        },
                        {
                            "term": {
                                "sampleProperty": {
                                    "value": "",
                                    "boost": 1.0
                                }
                            }
                        }
                    ]
                    """;

            JSONObject boolJson = new JSONObject();
            boolJson.put("adjust_pure_negative", true);
            boolJson.put("boost", 1.0);
            boolJson.put("must", JSON.parseArray(must));
            boolJson.put("must_not", JSON.parseArray(mustNot));

            JSONObject queryJson = new JSONObject();
            queryJson.put("bool", boolJson);

            JSONObject searchJson = new JSONObject();
            searchJson.put("_source", Arrays.asList("finalCheckDate", "hspOrgCode", "hspOrgName", "barcode", "outBarcode", "sampleProperty", "samplePropertyCode"));
            searchJson.put("query", queryJson);
            searchJson.put("from", 0);
            searchJson.put("size", 1000);

            // 4. 封装 Header 并发送请求
            HttpEntity<String> requestEntity = new HttpEntity<>(searchJson.toJSONString(), headers); // GET 请求可不传 Body
            ResponseEntity<String> response = restTemplate.exchange(
                    "http://10.140.0.129:9200/lims-sample-dongguan/_doc/_search",
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            System.out.println("response.getBody() = " + getResults(response.getBody()));

            exportToExcel(getResults(response.getBody()).stream().map(e -> toESData((JSONObject) e)).sorted(Comparator.comparing(ESData::getFinalCheckDate)).collect(Collectors.toList()));

        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

    private static void exportToExcel(List<ESData> datas) throws Exception {
        File exportFile = new File("样本性状异常数据.xlsx");
        if (exportFile.exists()) {
            System.out.println(StrUtil.format("删除已存在的文件 {}", exportFile.delete()));
        }
        System.out.println(StrUtil.format("创建文件 {}", exportFile.createNewFile()));

        List<List<String>> headers = new ArrayList<>();
        headers.add(new ArrayList<>() {{add("审核时间");}});
        headers.add(new ArrayList<>() {{add("送检机构编码");}});
        headers.add(new ArrayList<>() {{add("送检机构名称");}});
        headers.add(new ArrayList<>() {{add("条码号");}});
        headers.add(new ArrayList<>() {{add("外部条码号");}});
        headers.add(new ArrayList<>() {{add("样本性状编码");}});
        headers.add(new ArrayList<>() {{add("样本性状描述");}});
        // 这里 需要指定写用哪个class去写
        try (ExcelWriter excelWriter = EasyExcel.write(exportFile, ESData.class).head(headers).build()) {

            System.out.println(StrUtil.format("数据量 {}", datas.size()));

            WriteSheet writeSheet = EasyExcel.writerSheet("样本性状异常数据").build();
            writeSheet.setCustomWriteHandlerList(new ArrayList<>() {{
                add(new LisDataProcess.MyHeadColumnWidthStyle(20));
            }});
            excelWriter.write(datas, writeSheet);

            /// 千万别忘记finish 会帮忙关闭流
            excelWriter.finish();
        }

    }


    private static JSONArray getResults(String responseBody) {
        return JSON.parseObject(responseBody).getJSONObject("hits").getJSONArray("hits");
    }

    private static ESData toESData(JSONObject hit) {
        JSONObject source = hit.getJSONObject("_source");
        ESData esData = source.toJavaObject(ESData.class);
        esData.setFinalCheckDate(DateUtil.format(new Date(source.getLong("finalCheckDate")), DatePattern.NORM_DATETIME_PATTERN));
        return esData;
    }

    @Data
    static class ESData {
        private String finalCheckDate;
        private String hspOrgCode;
        private String hspOrgName;
        private String barcode;
        private String outBarcode;
        private String samplePropertyCode;
        private String sampleProperty;
    }

}
