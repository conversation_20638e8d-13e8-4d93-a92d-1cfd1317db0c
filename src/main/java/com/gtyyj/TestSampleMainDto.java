package com.gtyyj;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Builder(toBuilder = true)
@Data
public class TestSampleMainDto {

    /**
     * 样本信息系统编号
     */
    private Long sampleMainSysno;

    /**
     * 仪器样本id
     */
    private String machineSampleId;

    /**
     * 样本信息ID
     */
    private String sampleMainId;

    /**
     * 检验申请单ID
     */
    private String testApplyId;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 主条码号
     */
    private String masterBarcode;

    /**
     * 展示的条码号
     */
    private String showBarcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 第三方条码号
     */
    private String otherBarcode;

    /**
     * 是否加急(0否1是)
     *
     */
    private Integer needUrgent;

    /**
     * 套餐ID
     */
    private String packageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 专业组
     */
    private String professionGroupId;

    /**
     * 专业组编码
     */
    private String professionGroupCode;

    /**
     * 专业组名称
     */
    private String professionGroup;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 样本类型编码
     */
    private String sampleTypeCode;

    /**
     * 管型
     */
    private String testTube;
    
    /**
     * 检验类型 ，1是检验 2是病理
     */
    private Integer checkType;

    /**
     * 管型编码
     */
    private String testTubeCode;

    /**
     * 样本状态
     *
     */
    private Integer sampleStatus;

    /**
     * 样本报告ID
     */
    private String sampleReportId;

    /**
     * 样本报告地址
     */
    private String sampleReportUrl;

    /**
     * 受检人id
     */
    private String patientId;

    /**
     * 受检人姓名
     */
    private String patientName;

    /**
     * 受检人证件类型
     *
     */
    private String patientCardType;

    /**
     * 受检人证件号码
     */
    private String patientCardNo;

    /**
     * 受检人就诊卡类型编码
     */
    private Integer visitCardTypeCode;

    /**
     * 受检人就诊卡类型
     */
    private Integer visitCardType;

    /**
     * 受检人就诊卡号
     */
    private String visitCardNo;

    /**
     * 受检人性别
     *
     */
    private Integer patientSex;

    /**
     * 受检人年龄
     */
    private Integer patientAge;

    /**
     * 受检人实际年龄
     */
    private Integer physicalAge;
    /**
     * 受检人实际年龄单位
     *
     */
    private String ageUnit;

    /**
     * 就诊类型编码
     *
     */
    private String sourceTypeCode;

    /**
     * 就诊类型名称
     *
     */
    private String sourceTypeName;

    /**
     * 就诊编号
     */
    private String sourceNo;

    /**
     * 送检医生编号
     */
    private String sendDoctorId;

    /**
     * 送检医生姓名
     */
    private String sendDoctorName;

    /**
     * 送检医生卡号
     */
    private String sendDoctorCard;

    /**
     * 样本数量
     */
    private Integer sampleCount;

    /**
     * 样本来源
     *
     */
    private Integer sampleSource;

    /**
     * 样本来源描述
     *
     */
    private String sampleSourceDesc;

    /**
     * 仪器设备ID
     */
    private String instrumentId;

    /**
     * 仪器设备名称
     */
    private String instrumentName;

    /**
     * 仪器设备编号
     */
    private String instrumentCode;

    /**
     * 仪器信息
     */
    private String instrumentInfo;
    /**
     * 可用仪器ID集合
     */
    private String instrumentIdCollection;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 采样时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime sampleDate;

    /**
     * 检验项目列表
     * <pre>
     *     [
     *          {
     *              "id":"检验项目ID",
     *              "name":"检验项目名称"
     *          }
     *     ]
     * </pre>
     */
    private String testItemList;


    /**
     * 报告项目列表
     * <pre>
     *     [
     *          {
     *              "id":"报告项目ID",
     *              "name":"报告项目名称"
     *          }
     *     ]
     * </pre>
     */
    private String testReportItemList;

    /**
     * 送检类型
     *
     */
    private String sendType;

    /**
     * 送检类型编码
     *
     */
    private Integer sendTypeCode;

    /**
     * 分割码
     */
    private String splitCode;

    /**
     * 打印状态
     *
     */
    private Integer printStatus;

    /**
     * 审核状态
     *
     */
    private Integer auditStatus;

    /**
     * 检验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime testDate;

    /**
     * 签收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signDate;

    /**
     * 是否外送
     *
     */
    private Integer isSend;

    /**
     * 检验人ID
     */
    private String testPeopleId;

    /**
     * 检验人
     */
    private String testPeople;


    /**
     * 接收人ID
     */
    private String receiverId;

    /**
     * 接收人姓名
     */
    private String receiver;

    /**
     * 审核人ID
     */
    private String auditorId;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 是否删除
     *
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 申请病区名称
     */
    private String inpatientAreaName;

    //新增字段区域

    /**
     * 临床诊断名称
     */
    private String diagnosisName;

    /**
     * 申请科室
     */
    private String deptName;
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;

    /**
     * 受检人床号
     */
    private String patientBedNo;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 送检机构Code
     */
    private String sendOrgCode;

    /**
     * 送检人（送中心）
     */
    private String sender;

    /**
     * 送检人ID（送中心）
     */
    private String senderId;

    /**
     * 送检机构名字
     */
    private String sendOrgName;

    /**
     * 结果状态
     */
    private String resultStatus;

    /**
     * 流水号
     */
    private String param1;

    /**
     * 流水号（团签）
     */
    private String param2;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receiveDate;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 外送时间
     */
    private LocalDateTime sendTime;

    /**
     * 审核时间
     */
    private String auditDate;

    /**
     * 是否已采样
     * 0否  1是
     */
    private Integer isGather;

    /**
     * 关联条码
     */
    private String relationBarcode;
    
    private String childCustomerId;
    
    private String childCustomerName;
    /**
     * 实验室接收时间
     */
    private LocalDateTime labReceiveDate;

    /**
     * 报告有效时间
     */
    private LocalDateTime reportValidTime;
    /**
     * 结果备注
     */
    private String resultRemark;

    private Apply apply;

    @Tolerate
    public TestSampleMainDto() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TestSampleMainDto that = (TestSampleMainDto) o;
        return Objects.equals(sampleMainId, that.sampleMainId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sampleMainId);
    }
}
