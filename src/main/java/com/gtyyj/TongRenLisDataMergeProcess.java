package com.gtyyj;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractHeadColumnWidthStyleStrategy;
import lombok.Data;
import lombok.ToString;
import org.apache.poi.ss.usermodel.Cell;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <pre>
 *     同仁体检数据处理
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/7/28 16:23
 */
public class TongRenLisDataMergeProcess {

    public static void main(String[] args) throws Exception {

        String year = args[0];

        final String exportFileName = StrUtil.format("D:\\兰卫工作\\社区云LIS\\数据导出\\同仁问卷数据与体检数据整合\\_{}_同仁体检数据.xlsx", year);
        File targetFile = new File(exportFileName);
        if (targetFile.exists()) {
            targetFile.delete();
        }

        targetFile.createNewFile();

        final File sourceDir = new File("D:\\兰卫工作\\社区云LIS\\数据导出\\同仁问卷数据与体检数据整合");

        AtomicInteger cnt = new AtomicInteger(0);
        AtomicInteger totalCnt = new AtomicInteger(0);

        Set<String> unique = new HashSet<>();
        final List<Row> list = new ArrayList<Row>();
        AnalysisEventListener<Row> readListener = new AnalysisEventListener<>() {
            /**
             * 这个每一条数据解析都会来调用
             */
            @Override
            public void invoke(Row data, AnalysisContext context) {
                // System.out.println("解析到一条数据:" + JSON.toJSONString(data));
                if (unique.add(data.toString())) {
                    list.add(data);
                    totalCnt.incrementAndGet();
                } else {
                    System.out.println("这条数据重复了 " + data.toString());
                }
            }

            /**
             * 所有数据解析完成了 都会来调用
             */
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {

            }
        };

        final String suffix = StrUtil.format("{}.xlsx", year);
        for (File file : Objects.requireNonNull(sourceDir.listFiles())) {
            if (!file.getName().endsWith(suffix)) {
                continue;
            }
            // file = new File("C:\\work\\文档\\社区云LIS\\上海内网\\北新泾\\Result0.xlsx");
            ExcelReader excelReader = EasyExcel.read(file).build();
            // 这里为了简单 所以注册了 同样的head 和Listener 自己使用功能必须不同的Listener
            // readSheet参数设置读取sheet的序号
            ReadSheet readSheet1 =
                    EasyExcel.readSheet(0).head(Row.class).registerReadListener(readListener).build();
            // 这里注意 一定要把sheet1 sheet2 一起传进去，不然有个问题就是03版的excel 会读取多次，浪费性能
            excelReader.read(readSheet1);
            // 这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
            excelReader.finish();

            System.out.println(StrUtil.format("文件 {} 数据总行数 {}", file.getName(), totalCnt.get()));

            cnt.addAndGet(totalCnt.get());
            totalCnt.set(0);
        }

        System.out.println("总条数 " + cnt.get());

        List<List<String>> headers = new ArrayList<>();
        headers.add(new ArrayList<>() {{
            add("所属社区");
        }});
        headers.add(new ArrayList<>() {{
            add("SFZH");
        }});
        headers.add(new ArrayList<>() {{
            add("性别");
        }});
        headers.add(new ArrayList<>() {{
            add("项目名称");
        }});
        headers.add(new ArrayList<>() {{
            add("结果");
        }});
        // 这里 需要指定写用哪个class去写
        try (ExcelWriter excelWriter = EasyExcel.write(targetFile, Row.class).head(headers).build()) {

            AtomicInteger exportCnt = new AtomicInteger(0);
            System.out.println(StrUtil.format("sheet {} 数据量 {}", year, list.size()));
            exportCnt.addAndGet(list.size());

            WriteSheet writeSheet = EasyExcel.writerSheet(StrUtil.format("{}({})", year, list.size())).build();
            writeSheet.setCustomWriteHandlerList(new ArrayList<>() {{
                add(new MyHeadColumnWidthStyle(20));
            }});
            excelWriter.write(list, writeSheet);

            System.out.println("exportCnt = " + exportCnt);

            /// 千万别忘记finish 会帮忙关闭流
            excelWriter.finish();
        }

    }

    @Data
    @ToString
    public static class Row {
        @ExcelProperty(index = 0)
        private String customerName;
        @ExcelProperty(index = 1)
        private String patientCardNo;
        @ExcelProperty(index = 2)
        private String patientSex;
        @ExcelProperty(index = 3)
        private String itemName;
        @ExcelProperty(index = 4)
        private String testResult;
    }

    static class MyHeadColumnWidthStyle extends AbstractHeadColumnWidthStyleStrategy {
        private final Integer columnWidth;

        public MyHeadColumnWidthStyle(Integer columnWidth) {
            this.columnWidth = columnWidth;
        }

        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            boolean needSetWidth = relativeRowIndex != null && (isHead || relativeRowIndex == 0);
            if (needSetWidth) {
                Integer width = this.columnWidth(head, cell.getColumnIndex());
                if (width != null) {
                    width = width * 256;
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), width);
                }
            }
        }

        protected Integer columnWidth(Head head, Integer columnIndex) {
            return this.columnWidth;
        }
    }

}
