package com.gtyyj.mybatis;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;

import javax.sql.DataSource;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * LISPatientInfoEnctyptMySQL
 * 社区LIS用户信息脱敏
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/7 17:21
 */
public class LISPatientInfoEnctyptMySQL {

    public static void main(String[] args) throws Exception {
        try {
            try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
                TbPatientInfoMapper tbPatientInfoMapper = sqlSession.getMapper(TbPatientInfoMapper.class);

                /*TbPatientInfo tbPatientInfo = tbPatientInfoMapper.selectById(1L);
                System.out.println(tbPatientInfo.getPatientName());
                tbPatientInfo.setPatientName(UltraShortAESExample.encryptUltraShort(tbPatientInfo.getPatientName()));
                tbPatientInfoMapper.updatePatientInfoById(tbPatientInfo);*/

                List<TbPatientInfo> tbPatientInfos = tbPatientInfoMapper.selectAll(null);
                System.out.println(tbPatientInfos.size());

                int count = tbPatientInfoMapper.count(null);
                System.out.println("count = " + count);

                for (TbPatientInfo tbPatientInfo : tbPatientInfos) {
                    /*final String patientName = tbPatientInfo.getPatientName();
                    final String patientCardNo = tbPatientInfo.getPatientCardNo();
                    final String patientAddr = tbPatientInfo.getPatientAddr();
                    final String patientTel = tbPatientInfo.getPatientTel();
                    final String patientMedNumber = tbPatientInfo.getPatientMedNumber();*/
                    final String patientAge = tbPatientInfo.getPatientAge();

                    /*if (StringUtils.isNotBlank(patientName)) {
                        tbPatientInfo.setPatientName(UltraShortAESExample.encryptUltraShort(patientName));
                    }
                    if (StringUtils.isNotBlank(patientCardNo)) {
                        tbPatientInfo.setPatientCardNo(UltraShortAESExample.encryptUltraShort(patientCardNo));
                    }
                    if (StringUtils.isNotBlank(patientAddr)) {
                        tbPatientInfo.setPatientAddr(UltraShortAESExample.encryptUltraShort(patientAddr));
                    }
                    if (StringUtils.isNotBlank(patientTel)) {
                        tbPatientInfo.setPatientTel(UltraShortAESExample.encryptUltraShort(patientTel));
                    }
                    if (StringUtils.isNotBlank(patientMedNumber)) {
                        tbPatientInfo.setPatientMedNumber(UltraShortAESExample.encryptUltraShort(patientMedNumber));
                    }*/
                    if (StringUtils.isNotBlank(patientAge)) {
                        tbPatientInfo.setPatientAge(UltraShortAESExample.encryptUltraShort(patientAge));
                    }

                    System.out.println(tbPatientInfoMapper.updatePatientInfoById(tbPatientInfo));

                    TimeUnit.MILLISECONDS.sleep(20L);
                }

                sqlSession.commit();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    static SqlSessionFactory sqlSessionFactory;

    static {
        try {
            initSqlsession();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static DataSource createDatasource() {
        // 1. 创建数据源
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("***************************************************************************************************************************************************************************************************************************************");
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUsername("root");
        dataSource.setPassword("Huawei@1234!");

        return dataSource;
    }

    public static void initSqlsession() throws Exception {

        TransactionFactory transactionFactory = new JdbcTransactionFactory();
        Environment environment = new Environment("development", transactionFactory, createDatasource());

        Configuration configuration = new Configuration(environment);
        configuration.getMapperRegistry().addMapper(TbPatientInfoMapper.class);
        // 如果你的 XML 位置特殊，先手动解析 XML，再执行上一步：
        String resource = "mapper/TbPatientInfoMapper.xml";
        InputStream inputStream = Resources.getResourceAsStream(resource);
        new XMLMapperBuilder(inputStream, configuration, resource, configuration.getSqlFragments()).parse();


        sqlSessionFactory = new SqlSessionFactoryBuilder().build(configuration);
        // TbPatientInfoMapper tbPatientInfoMapper = sqlSessionFactory.openSession().getMapper(TbPatientInfoMapper.class);

    }

}
