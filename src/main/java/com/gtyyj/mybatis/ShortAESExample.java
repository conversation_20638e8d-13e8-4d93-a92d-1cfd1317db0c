package com.gtyyj.mybatis;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPOutputStream;
import java.util.zip.GZIPInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;

public class ShortAESExample {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    // 请使用安全的密钥生成方式，此处为演示使用固定密钥
    private static final String SECRET_KEY = "TJmKeWOtr0WXPmub"; // 128位密钥应为16字节

    /**
     * 加密并压缩原始数据
     *
     * @param plaintext 明文数据
     * @return 经过Base64编码的加密字符串
     */
    public static String encrypt(String plaintext) throws Exception {
        // 1. 压缩原始数据
        byte[] compressedData = compressData(plaintext.getBytes(StandardCharsets.UTF_8));
        
        // 2. 生成AES密钥
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        
        // 3. 初始化加密器
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        
        // 4. 执行加密操作
        byte[] encryptedBytes = cipher.doFinal(compressedData);
        
        // 5. 将加密后的二进制数据转换为Base64URL字符串（无填充）
        return Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedBytes);
    }

    /**
     * 解密并解压缩数据
     *
     * @param encryptedBase64 经过Base64编码的加密字符串
     * @return 原始明文数据
     */
    public static String decrypt(String encryptedBase64) throws Exception {
        // 1. 将Base64URL字符串解码为二进制数据
        byte[] encryptedBytes = Base64.getUrlDecoder().decode(encryptedBase64);
        
        // 2. 生成AES密钥
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        
        // 3. 初始化解密器
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        
        // 4. 执行解密操作
        byte[] decryptedCompressedData = cipher.doFinal(encryptedBytes);
        
        // 5. 解压缩数据
        byte[] decompressedData = decompressData(decryptedCompressedData);
        
        return new String(decompressedData, StandardCharsets.UTF_8);
    }

    /**
     * 使用GZIP压缩数据
     */
    private static byte[] compressData(byte[] data) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream(data.length);
        try (GZIPOutputStream gzipOS = new GZIPOutputStream(bos)) {
            gzipOS.write(data);
        }
        return bos.toByteArray();
    }

    /**
     * 使用GZIP解压缩数据
     */
    private static byte[] decompressData(byte[] compressedData) throws IOException {
        ByteArrayInputStream bis = new ByteArrayInputStream(compressedData);
        try (GZIPInputStream gzipIS = new GZIPInputStream(bis);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipIS.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toByteArray();
        }
    }

    public static void main(String[] args) {
        try {
            String originalText = "210235199912314567";

            // 加密
            String encryptedText = encrypt(originalText);
            System.out.println("加密后的Base64字符串: " + encryptedText);
            System.out.println("加密后字符串长度: " + encryptedText.length());

            // 解密
            String decryptedText = decrypt(encryptedText);
            System.out.println("解密后的文本: " + decryptedText);

            // 验证原始数据和解密数据是否一致
            System.out.println("数据一致性验证: " + originalText.equals(decryptedText));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}