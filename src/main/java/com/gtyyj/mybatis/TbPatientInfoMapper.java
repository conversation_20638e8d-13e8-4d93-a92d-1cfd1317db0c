package com.gtyyj.mybatis;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbPatientInfoMapper {

    int updatePatientInfoById(@Param("patient") TbPatientInfo patient);

    TbPatientInfo selectById(@Param("patientSysno") Long patientSysno);

    List<TbPatientInfo> selectAll(@Param("patient") TbPatientInfo patient);

    int count(@Param("patient") TbPatientInfo patient);

}
