package com.gtyyj;

import com.alibaba.fastjson.JSON;
import com.swak.frame.util.StringPool;
import lombok.Data;
import lombok.experimental.Accessors;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/26 15:33
 */
public class BizSignLogAnalysis {

    static final String END = "申请单签收【结果";
    static final String START = "申请单签收【参数";

    public static void main(String[] args) throws Exception {
        Map<String, String> executeTimeMap = new HashMap<>();

        Path path = Paths.get("C:\\Users\\<USER>\\Downloads\\Explore-logs-2024-04-26 15_30_52.txt");
        Files.lines(path).forEach(line -> {
            String barcode = getBarcode(line);
            String executeAt = getExecuteTime(line);
            String executeTime = executeTimeMap.getOrDefault(barcode, StringPool.POUND);
            if (line.contains(END)) {
                executeTime = executeTime + executeAt;
            } else {
                executeTime = executeAt + executeTime;
            }
            executeTimeMap.put(barcode, executeTime);
        });

        System.out.println(JSON.toJSONString(executeTimeMap));

        List<SignTime> costs = executeTimeMap.entrySet().stream().map(entry -> {
            String[] split = entry.getValue().split(StringPool.POUND);
            long cost = parse(split[1]).getTime() - parse(split[0]).getTime();
            return new SignTime().setBarcode(entry.getKey()).setCost(cost).setExecuteAt(split[1]);
        }).collect(Collectors.toList());

        costs.sort((o1, o2) -> o2.getCost().compareTo(o1.getCost()));

        System.out.println(JSON.toJSONString(costs));

    }

    public static Date parse(String date) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse(date);
        } catch (ParseException e) {
            return new Date();
        }
    }

    @Data
    @Accessors(chain = true)
    static class SignTime {
        private String barcode;
        private String executeAt;
        private Long cost;
    }

    static final int len = "200018376600".length();

    public static String getBarcode(String line) {
        String barcode = "barcode";
        int index = line.indexOf(barcode) + barcode.length() + 3;
        return line.substring(index, index + len);
    }

    public static String getExecuteTime(String line) {
        int i = line.indexOf("\t") + 1;
        return line.substring(i, i + 23);
    }

}
