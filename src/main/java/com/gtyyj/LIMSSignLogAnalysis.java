package com.gtyyj;

import com.alibaba.fastjson.JSON;
import com.swak.frame.util.StringPool;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/26 15:33
 */
public class LIMSSignLogAnalysis {

    static final String END = "专业组 [前处理组] 条码签收成功";
    static final String START = "签收入参===>";

    public static void main(String[] args) throws Exception {
        Map<String, String> executeTimeMap = new HashMap<>();

        Path path = Paths.get("C:\\Users\\<USER>\\Downloads\\LIMS-SIGN-logs-2024-04-26 17_09_32.txt");
        Files.lines(path).forEach(line -> {
            String barcode = getBarcode(line);
            String executeAt = getExecuteTime(line);
            String executeTime = executeTimeMap.getOrDefault(barcode, StringPool.POUND);
            if (line.contains(END)) {
                executeTime = executeTime + executeAt;
            } else {
                executeTime = executeAt + executeTime;
            }
            executeTimeMap.put(barcode, executeTime);
        });

        System.out.println(JSON.toJSONString(executeTimeMap));

        List<SignTime> costs = executeTimeMap.entrySet().stream().filter(e -> e.getValue().split(StringPool.POUND).length == 2).map(entry -> {
            String[] split = entry.getValue().split(StringPool.POUND);
            long cost = parse(split[1]).getTime() - parse(split[0]).getTime();
            return new SignTime().setBarcode(entry.getKey()).setCost(cost).setExecuteAt(split[1]);
        }).collect(Collectors.toList());

        costs.sort((o1, o2) -> o2.getCost().compareTo(o1.getCost()));

        costs.forEach(cost -> System.out.println(cost.getBarcode() + "===>" + cost.getCost()));

    }

    public static Date parse(String date) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse(date);
        } catch (ParseException e) {
            return new Date();
        }
    }

    @Data
    @Accessors(chain = true)
    static class SignTime {
        private String barcode;
        private String executeAt;
        private Long cost;
    }

    static Pattern pattern = Pattern.compile("\"Barcode\":\"(\\d+)\"|条码号：(\\d+)  结果");

    public static String getBarcode(String line) {
        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(line);

        String barcode = StringPool.EMPTY;
        // 查找匹配的字符串
        if (matcher.find()) {
            // 获取匹配到的内容
            for (int i = 1; i <= matcher.groupCount(); i++) {
                barcode = matcher.group(i);
                if (StringUtils.isNotBlank(barcode)) {
                    break;
                }
            }
        } else {
            barcode = null;
        }
        System.out.println("匹配到的 outBarcode 值：" + barcode + "\t" + line);
        return barcode;
    }

    public static String getExecuteTime(String line) {
        int i = line.indexOf("\t") + 1;
        return line.substring(i, i + 23);
    }

}
