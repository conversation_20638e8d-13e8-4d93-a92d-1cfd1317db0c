package com.gtyyj;

import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <pre>
 * ObjUtils
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/13 15:01
 */
@Slf4j
public class ObjUtils {

    public static <T> T fillDefaultValue(T obj) {
        if (obj == null) {
            return null;
        }
        try {
            for (Field field : obj.getClass().getDeclaredFields()) {
                field.setAccessible(true);

                final Class<?> type = field.getType();
                final Object defaultVal;
                // 字符串
                if (Objects.equals(String.class, type)) {
                    defaultVal = StringPool.EMPTY;
                }
                // BigDecimal
                else if (Objects.equals(BigDecimal.class, type)) {
                    defaultVal = BigDecimal.ZERO;
                }
                // Integer
                else if (Objects.equals(Integer.class, type)) {
                    defaultVal = 0;
                }
                // Long
                else if (Objects.equals(Long.class, type)) {
                    defaultVal = 0L;
                }
                // Float
                else if (Objects.equals(Float.class, type)) {
                    defaultVal = 0F;
                }
                // Double
                else if (Objects.equals(Double.class, type)) {
                    defaultVal = 0D;
                }
                // Byte
                else if (Objects.equals(Byte.class, type)) {
                    defaultVal = Byte.valueOf("0");
                }
                // Short
                else if (Objects.equals(Short.class, type)) {
                    defaultVal = Short.valueOf("0");
                }
                // 日期
                else if (Objects.equals(Date.class, type)) {
                    defaultVal = new Date();
                }
                // 日期时间
                else if (Objects.equals(LocalDateTime.class, type)) {
                    defaultVal = LocalDateTime.now();
                }
                // 日期
                else if (Objects.equals(LocalDate.class, type)) {
                    defaultVal = LocalDate.now();
                }
                // 时间
                else if (Objects.equals(LocalTime.class, type)) {
                    defaultVal = LocalTime.now();
                }
                // Boolean
                else if (Objects.equals(Boolean.class, type)) {
                    defaultVal = Boolean.FALSE;
                }
                // List
                else if (Objects.equals(List.class, type)) {
                    defaultVal = Collections.emptyList();
                }
                // Set
                else if (Objects.equals(Set.class, type)) {
                    defaultVal = Collections.emptySet();
                }
                // Map
                else if (Objects.equals(Map.class, type)) {
                    defaultVal = Collections.emptyMap();
                }
                // 数组类型
                else if (type.isArray()) {
                    defaultVal = java.lang.reflect.Array.newInstance(type.getComponentType(), 0);
                }
                // 其他对象类型
                else {
                    defaultVal = type.getConstructors().length > 0 ? type.getConstructors()[0].newInstance() : null;
                    if (defaultVal != null) {
                        fillDefaultValue(defaultVal);
                    }
                }

                field.set(obj, defaultVal);
            }
        } catch (Exception e) {
            log.warn("填充默认值异常 ", e);
        }

        return obj;
    }

    public static void main(String[] args) {
        TestSampleMainDto testSampleMainDto = new TestSampleMainDto();
        fillDefaultValue(testSampleMainDto);
        System.out.println(testSampleMainDto);
    }

}
