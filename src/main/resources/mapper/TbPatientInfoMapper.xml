<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtyyj.mybatis.TbPatientInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gtyyj.mybatis.TbPatientInfo">
        <id column="patient_sysno" property="patientSysno"/>
        <result column="patient_name" property="patientName"/>
        <result column="patient_card_no" property="patientCardNo"/>
        <result column="patient_addr" property="patientAddr"/>
        <result column="patient_tel" property="patientTel"/>
        <result column="patient_med_number" property="patientMedNumber"/>
        <result column="patient_age" property="patientAge"/>
    </resultMap>

    <update id="updatePatientInfoById" parameterType="com.gtyyj.mybatis.TbPatientInfo">
        UPDATE tb_patient_info SET
        patient_name = #{patient.patientName}
        <if test="patient.patientCardNo != null and patient.patientCardNo != ''">
            ,patient_card_no = #{patient.patientCardNo}
        </if>
        <if test="patient.patientAddr != null and patient.patientAddr != ''">
            ,patient_addr = #{patient.patientAddr}
        </if>
        <if test="patient.patientTel != null and patient.patientTel != ''">
            ,patient_tel = #{patient.patientTel}
        </if>
        <if test="patient.patientMedNumber != null and patient.patientMedNumber != ''">
            ,patient_med_number = #{patient.patientMedNumber}
        </if>
        <if test="patient.patientAge != null and patient.patientAge != ''">
            ,patient_age = #{patient.patientAge}
        </if>
        WHERE
        patient_sysno = #{patient.patientSysno}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM tb_patient_info
        where patient_sysno = #{patientSysno}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM tb_patient_info
        where is_delete = 0 and patient_sysno >= 60000
        <if test="patient != null">
            <if test="patient.patientName != null">
                and patient_name like concat('%',#{patient.patientName},'%')
            </if>
            <if test="patient.patientCardNo != null">
                and patient_card_no = #{patient.patientCardNo}
            </if>
            <if test="patient.patientAddr != null">
                and patient_addr like concat('%',#{patient.patientAddr},'%')
            </if>
            <if test="patient.patientTel != null">
                and patient_tel = #{patient.patientTel}
            </if>
        </if>
        order by create_time desc
    </select>

    <select id="count" resultType="int">
        SELECT count(*)
        FROM tb_patient_info
        where is_delete = 0
        <if test="patient != null">
            <if test="patient.patientName != null">
                and patient_name like concat('%',#{patient.patientName},'%')
            </if>
            <if test="patient.patientCardNo != null">
                and patient_card_no = #{patient.patientCardNo}
            </if>
            <if test="patient.patientAddr != null">
                and patient_addr like concat('%',#{patient.patientAddr},'%')
            </if>
            <if test="patient.patientTel != null">
                and patient_tel = #{patient.patientTel}
            </if>
        </if>
    </select>

</mapper>
